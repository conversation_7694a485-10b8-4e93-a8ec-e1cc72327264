import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Download,
  Upload,
  Eye,
  EyeOff,
  Save,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PageContainer } from '@/components/layout/PageContainer';
import { StandardCard } from '@/components/ui/standard-card';
import { useTheme } from 'next-themes';
import { useAuthStore } from '@/stores/useAuthStore';
import { useGameStore } from '@/stores/useGameStore';

export const Settings: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const { user } = useAuthStore();
  const { userGames } = useGameStore();
  
  // Profile state
  const [email, setEmail] = useState(user?.email || '');
  const [username, setUsername] = useState(user?.username || '');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPasswords, setShowPasswords] = useState(false);
  
  // App preferences
  const [showCompletedGames, setShowCompletedGames] = useState(true);
  const [enableNotifications, setEnableNotifications] = useState(false);
  const [autoBackup, setAutoBackup] = useState(false);
  
  // Loading states
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const handleSaveProfile = async () => {
    setSaving(true);
    setMessage(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMessage({ type: 'success', text: 'Profile updated successfully!' });
    } catch {
      setMessage({ type: 'error', text: 'Failed to update profile. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async () => {
    if (newPassword !== confirmPassword) {
      setMessage({ type: 'error', text: 'New passwords do not match.' });
      return;
    }
    
    if (newPassword.length < 8) {
      setMessage({ type: 'error', text: 'Password must be at least 8 characters long.' });
      return;
    }

    setSaving(true);
    setMessage(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setMessage({ type: 'success', text: 'Password updated successfully!' });
    } catch {
      setMessage({ type: 'error', text: 'Failed to update password. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleExportData = () => {
    const data = {
      user: { email, username },
      games: userGames,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gamevault-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    setMessage({ type: 'success', text: 'Data exported successfully!' });
  };

  const handleRestartOnboarding = () => {
    if (user?.id) {
      localStorage.removeItem(`onboarding-completed-${user.id}`);
      setMessage({ type: 'success', text: 'Onboarding will show on next page refresh.' });
    }
  };

  return (
    <PageContainer
      title="Settings"
      description="Manage your account and application preferences"
      spacing="md"
    >
      {message && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        </motion.div>
      )}

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 h-auto p-1 bg-muted/50">
          <TabsTrigger value="profile" className="text-xs sm:text-sm py-2 sm:py-2.5">Profile</TabsTrigger>
          <TabsTrigger value="appearance" className="text-xs sm:text-sm py-2 sm:py-2.5">Appearance</TabsTrigger>
          <TabsTrigger value="preferences" className="text-xs sm:text-sm py-2 sm:py-2.5">Preferences</TabsTrigger>
          <TabsTrigger value="data" className="text-xs sm:text-sm py-2 sm:py-2.5">Data</TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-4 lg:space-y-6">
          <StandardCard
            title="Profile Information"
            description="Update your account details and personal information"
            size="md"
            hover={true}
            animate={true}
            delay={0}
          >
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={saving}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={saving}
                    placeholder="Optional display name"
                  />
                </div>
              </div>
              <Button onClick={handleSaveProfile} disabled={saving}>
                {saving ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Profile
                  </>
                )}
              </Button>
            </div>
          </StandardCard>

          <StandardCard
            title="Change Password"
            description="Update your password to keep your account secure"
            size="md"
            hover={true}
            animate={true}
            delay={0.1}
          >
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Current Password</Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    type={showPasswords ? 'text' : 'password'}
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    disabled={saving}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPasswords(!showPasswords)}
                  >
                    {showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type={showPasswords ? 'text' : 'password'}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    disabled={saving}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type={showPasswords ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={saving}
                  />
                </div>
              </div>
              <Button 
                onClick={handleChangePassword} 
                disabled={saving || !currentPassword || !newPassword || !confirmPassword}
              >
                {saving ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update Password'
                )}
              </Button>
            </div>
          </StandardCard>
        </TabsContent>

        {/* Appearance Tab */}
        <TabsContent value="appearance" className="space-y-6">
          <StandardCard
            title="Theme"
            description="Choose how GameVault looks and feels"
            size="md"
            hover={true}
            animate={true}
            delay={0}
          >
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Color Theme</Label>
                <Select value={theme} onValueChange={setTheme}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  System will match your device's theme preference
                </p>
              </div>
            </div>
          </StandardCard>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <StandardCard
            title="Display Preferences"
            description="Customize how your games and collection are displayed"
            size="md"
            hover={true}
            animate={true}
            delay={0}
          >
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Show Completed Games</Label>
                  <p className="text-sm text-muted-foreground">
                    Display completed games in your main collection view
                  </p>
                </div>
                <Switch
                  checked={showCompletedGames}
                  onCheckedChange={setShowCompletedGames}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications about price drops and updates
                  </p>
                </div>
                <Switch
                  checked={enableNotifications}
                  onCheckedChange={setEnableNotifications}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label>Auto Backup</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically backup your collection data weekly
                  </p>
                </div>
                <Switch
                  checked={autoBackup}
                  onCheckedChange={setAutoBackup}
                />
              </div>
            </div>
          </StandardCard>

          <StandardCard
            title="Onboarding"
            description="Restart the welcome tour to learn about GameVault features"
            size="md"
            hover={true}
            animate={true}
            delay={0.1}
          >
            <Button variant="outline" onClick={handleRestartOnboarding}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Restart Welcome Tour
            </Button>
          </StandardCard>
        </TabsContent>

        {/* Data Tab */}
        <TabsContent value="data" className="space-y-6">
          <StandardCard
            title="Export Data"
            description="Download a backup of your game collection and preferences"
            size="md"
            hover={true}
            animate={true}
            delay={0}
          >
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Export includes: your game collection, ratings, notes, and account preferences.
                This data can be used to restore your collection if needed.
              </p>
              <Button onClick={handleExportData}>
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
            </div>
          </StandardCard>

          <StandardCard
            title="Import Data"
            description="Restore your collection from a previous backup"
            size="md"
            hover={true}
            animate={true}
            delay={0.1}
          >
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Import a previously exported backup file to restore your collection.
              </p>
              <Input type="file" accept=".json" disabled />
              <Button disabled variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Import Data (Coming Soon)
              </Button>
            </div>
          </StandardCard>

          <StandardCard
            title="Danger Zone"
            description="Irreversible actions that will permanently affect your account"
            size="md"
            hover={true}
            animate={true}
            delay={0.2}
            variant="outlined"
            className="border-destructive bg-destructive/5"
          >
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Delete Account</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
                <Button variant="destructive" disabled>
                  Delete Account (Coming Soon)
                </Button>
              </div>
            </div>
          </StandardCard>
        </TabsContent>
      </Tabs>
    </PageContainer>
  );
};