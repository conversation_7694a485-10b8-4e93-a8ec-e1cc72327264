import React, { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { ResponsiveSidebar } from './ResponsiveSidebar';
import { Header } from './Header';
import { Footer } from './Footer';
import { MobileBottomNavigation } from './MobileBottomNavigation';

export const Layout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  // Don't show mobile navigation on landing page
  const isLandingPage = location.pathname === '/landing';

  return (
    <div className="min-h-screen bg-background">
      {/* Responsive Sidebar */}
      <ResponsiveSidebar isOpen={sidebarOpen} onClose={closeSidebar} />
      
      {/* Main Content */}
      <div className="lg:pl-72">
        <div className="flex flex-col min-h-screen">
          <Header onSidebarToggle={toggleSidebar} />
          <main className="flex-1 w-full min-h-[calc(100vh-4rem)]">
            <Outlet />
          </main>
          <Footer />
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      {!isLandingPage && <MobileBottomNavigation />}
    </div>
  );
};