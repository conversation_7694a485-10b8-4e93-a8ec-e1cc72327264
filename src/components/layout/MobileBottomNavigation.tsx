import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  Library,
  Plus,
  Heart,
  Settings,
} from 'lucide-react';
import { cn } from '@/lib/utils';

const mobileNavigation = [
  { name: 'Home', href: '/', icon: Home },
  { name: 'Collection', href: '/collection', icon: Library },
  { name: 'Add Game', href: '/add-game', icon: Plus },
  { name: 'Wishlist', href: '/wishlist', icon: Heart },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export const MobileBottomNavigation: React.FC = () => {
  const location = useLocation();

  return (
    <motion.nav
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-t from-background via-background/98 to-background/95 backdrop-blur-xl border-t border-border/40 shadow-2xl shadow-black/10 md:hidden"
    >
      <div className="flex items-center justify-around px-2 py-2 safe-area-inset-bottom">
        {mobileNavigation.map((item, index) => {
          const isActive = location.pathname === item.href;
          const isAddGame = item.href === '/add-game';
          
          return (
            <motion.div
              key={item.name}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ 
                duration: 0.3, 
                delay: index * 0.1,
                type: "spring",
                stiffness: 300,
                damping: 25
              }}
              className="flex-1 flex justify-center"
            >
              <NavLink
                to={item.href}
                className={cn(
                  "relative flex flex-col items-center justify-center px-3 py-2 rounded-2xl transition-all duration-300 group min-w-[64px] min-h-[64px]",
                  isActive
                    ? "bg-gradient-to-b from-primary to-primary/90 text-primary-foreground shadow-lg shadow-primary/30"
                    : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground active:scale-95"
                )}
              >
                <motion.div
                  className={cn(
                    "relative flex items-center justify-center transition-all duration-300",
                    isAddGame && !isActive ? "bg-primary/10 rounded-full p-2" : "",
                    isAddGame && isActive ? "bg-primary-foreground/20 rounded-full p-2" : ""
                  )}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  animate={isActive ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 0.3 }}
                >
                  <item.icon className={cn(
                    "h-5 w-5 transition-all duration-300",
                    isActive ? "drop-shadow-sm" : "group-hover:scale-110",
                    isAddGame ? "h-6 w-6" : ""
                  )} />
                  
                  {isActive && (
                    <motion.div
                      className="absolute -inset-2 bg-primary-foreground/10 rounded-full blur-sm"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </motion.div>
                
                <span className={cn(
                  "text-xs font-medium mt-1 transition-all duration-300 text-center",
                  isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-accent-foreground"
                )}>
                  {item.name}
                </span>
                
                {isActive && (
                  <motion.div
                    layoutId="mobile-nav-active"
                    className="absolute -top-0.5 left-1/2 -translate-x-1/2 w-8 h-1 bg-primary-foreground/60 rounded-full"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}
              </NavLink>
            </motion.div>
          );
        })}
      </div>
    </motion.nav>
  );
};