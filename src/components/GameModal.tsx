import { Game } from '../types';

interface GameModalProps {
  game: Game | null;
  isOpen: boolean;
  onClose: () => void;
}

export function GameModal({ game, isOpen, onClose }: GameModalProps) {
  if (!isOpen || !game) return null;

  const getPlatformIcon = (platform: string) => {
    const platformLower = platform?.toLowerCase() || '';
    if (platformLower.includes('playstation') || platformLower.includes('ps')) return '🎮';
    if (platformLower.includes('xbox')) return '🎯';
    if (platformLower.includes('nintendo') || platformLower.includes('switch')) return '🕹️';
    if (platformLower.includes('pc') || platformLower.includes('windows')) return '💻';
    if (platformLower.includes('mac')) return '🖥️';
    if (platformLower.includes('ios') || platformLower.includes('iphone')) return '📱';
    if (platformLower.includes('android')) return '🤖';
    if (platformLower.includes('steam')) return '🚂';
    return '🎮';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between rounded-t-xl">
          <div className="flex items-center gap-3">
            <h2 className="text-2xl font-bold text-gray-900">{game.title}</h2>
            <span className={`px-2 py-1 text-xs rounded-full font-medium ${
              game.id.startsWith('igdb_') 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-green-100 text-green-800'
            }`}>
              {game.id.startsWith('igdb_') ? 'IGDB' : 'RAWG'}
            </span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl font-bold w-8 h-8 flex items-center justify-center"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Cover & Basic Info */}
            <div className="lg:col-span-1">
              {game.cover_image && (
                <div className="aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden mb-4">
                  <img
                    src={game.cover_image}
                    alt={game.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Platform */}
              {game.platform && (
                <div className="mb-4">
                  <span className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg font-medium border border-blue-200">
                    <span className="text-lg">{getPlatformIcon(game.platform)}</span>
                    {game.platform}
                  </span>
                </div>
              )}

              {/* Rating */}
              {game.metacritic_score && (
                <div className="mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">Metacritic Score:</span>
                    <span className={`px-3 py-1 rounded-lg text-sm font-bold ${
                      game.metacritic_score >= 80 ? 'bg-green-100 text-green-800' :
                      game.metacritic_score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {game.metacritic_score}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Details */}
            <div className="lg:col-span-2">
              {/* Genres */}
              {game.genres.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Genres</h3>
                  <div className="flex flex-wrap gap-2">
                    {game.genres.map((genre) => (
                      <span
                        key={genre}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                      >
                        {genre}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Game Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {game.developer && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Developer</span>
                    <p className="text-gray-900">{game.developer}</p>
                  </div>
                )}
                
                {game.publisher && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Publisher</span>
                    <p className="text-gray-900">{game.publisher}</p>
                  </div>
                )}
                
                {game.release_date && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Release Date</span>
                    <p className="text-gray-900">{new Date(game.release_date).getFullYear()}</p>
                  </div>
                )}
              </div>

              {/* Description */}
              {game.description && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                  <p className="text-gray-700 leading-relaxed">{game.description}</p>
                </div>
              )}

              {/* Screenshots */}
              {game.screenshots && game.screenshots.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Screenshots</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {game.screenshots.slice(0, 6).map((screenshot, index) => (
                      <div key={index} className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                        <img
                          src={screenshot}
                          alt={`${game.title} screenshot ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
