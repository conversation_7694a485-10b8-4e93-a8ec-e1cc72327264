import { useState } from 'react';
import { Game } from '../types';

interface GameSearchProps {
  onSearchResults: (games: Game[]) => void;
  onLoading: (loading: boolean) => void;
  onError: (error: string | null) => void;
}

export function GameSearch({ onSearchResults, onLoading, onError }: GameSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchTerm.trim()) {
      onError('Please enter a search term');
      return;
    }

    onLoading(true);
    onError(null);

    try {
      // Import the API client dynamically to avoid circular dependencies
      const { gameAPI } = await import('../lib/api');

      // Search both APIs
      const results = await gameAPI.searchBothAPIs(searchTerm.trim(), 20);

      // Convert IGDB games to our Game interface - create separate entries for each platform
      const igdbGames: Game[] = [];
      results.igdb.forEach(igdbGame => {
        const platforms = igdbGame.platforms || [{ name: 'Unknown' }];
        platforms.forEach((platform, index) => {
          igdbGames.push({
            id: `igdb_${igdbGame.id}_${index}`,
            title: igdbGame.name,
            platform: platform.name as any,
            genres: (igdbGame.genres?.map(g => g.name) || []) as any[],
            developer: igdbGame.involved_companies?.find(c => c.developer)?.company.name,
            publisher: igdbGame.involved_companies?.find(c => c.publisher)?.company.name,
            release_date: igdbGame.first_release_date
              ? new Date(igdbGame.first_release_date * 1000).toISOString().split('T')[0]
              : undefined,
            description: igdbGame.summary,
            cover_image: igdbGame.cover?.url ? `https:${igdbGame.cover.url.replace('t_thumb', 't_cover_big')}` : undefined,
            screenshots: igdbGame.screenshots?.map(s => `https:${s.url.replace('t_thumb', 't_screenshot_med')}`) || [],
            metacritic_score: igdbGame.aggregated_rating ? Math.round(igdbGame.aggregated_rating) : undefined,
            igdb_id: igdbGame.id.toString(),
          });
        });
      });

      // Convert RAWG games to our Game interface - create separate entries for each platform
      const rawgGames: Game[] = [];
      results.rawg.forEach(rawgGame => {
        const platforms = rawgGame.platforms || [{ platform: { name: 'Unknown' } }];
        platforms.forEach((platformObj, index) => {
          rawgGames.push({
            id: `rawg_${rawgGame.id}_${index}`,
            title: rawgGame.name,
            platform: platformObj.platform?.name as any,
            genres: (rawgGame.genres?.map(g => g.name) || []) as any[],
            developer: rawgGame.developers?.[0]?.name,
            publisher: rawgGame.publishers?.[0]?.name,
            release_date: rawgGame.released,
            description: rawgGame.description_raw,
            cover_image: rawgGame.background_image,
            screenshots: rawgGame.short_screenshots?.map(s => s.image) || [],
            metacritic_score: rawgGame.metacritic,
            igdb_id: `rawg_${rawgGame.id}`,
          });
        });
      });

      // Combine results from both APIs
      const allGames = [...igdbGames, ...rawgGames];
      onSearchResults(allGames);
    } catch (error) {
      console.error('Search error:', error);
      onError(error instanceof Error ? error.message : 'Failed to search games');
      onSearchResults([]);
    } finally {
      onLoading(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Game Search</h1>
        <p className="text-gray-600">Search for games using IGDB and RAWG databases</p>
        <div className="flex justify-center gap-4 mt-2">
          <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">IGDB API</span>
          <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full font-medium">RAWG API</span>
        </div>
      </div>
      
      <form onSubmit={handleSearch} className="flex gap-4">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Enter game title..."
          className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
        />
        <button
          type="submit"
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium text-lg transition-colors"
        >
          Search
        </button>
      </form>
    </div>
  );
}
