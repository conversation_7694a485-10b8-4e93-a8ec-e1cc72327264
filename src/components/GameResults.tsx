import { Game } from '../types';

interface GameResultsProps {
  games: Game[];
  loading: boolean;
  error: string | null;
}

export function GameResults({ games, loading, error }: GameResultsProps) {
  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Searching for games...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Search Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (games.length === 0) {
    return null; // Don't show anything if no search has been performed yet
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          Search Results ({games.length} games found)
        </h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {games.map((game) => (
          <GameCard key={game.id} game={game} />
        ))}
      </div>
    </div>
  );
}

interface GameCardProps {
  game: Game;
}

function GameCard({ game }: GameCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      {game.cover_image && (
        <div className="aspect-[3/4] bg-gray-200">
          <img
            src={game.cover_image}
            alt={game.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
        </div>
      )}
      
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-bold text-lg text-gray-900 line-clamp-2 flex-1">
            {game.title}
          </h3>
          <span className={`ml-2 px-2 py-1 text-xs rounded-full font-medium ${
            game.id.startsWith('igdb_')
              ? 'bg-blue-100 text-blue-800'
              : 'bg-green-100 text-green-800'
          }`}>
            {game.id.startsWith('igdb_') ? 'IGDB' : 'RAWG'}
          </span>
        </div>
        
        {game.platform && (
          <p className="text-sm text-blue-600 mb-2">{game.platform}</p>
        )}
        
        {game.genres.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-2">
            {game.genres.slice(0, 3).map((genre) => (
              <span
                key={genre}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
              >
                {genre}
              </span>
            ))}
            {game.genres.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                +{game.genres.length - 3} more
              </span>
            )}
          </div>
        )}
        
        {game.developer && (
          <p className="text-sm text-gray-600 mb-1">
            <span className="font-medium">Developer:</span> {game.developer}
          </p>
        )}
        
        {game.publisher && (
          <p className="text-sm text-gray-600 mb-1">
            <span className="font-medium">Publisher:</span> {game.publisher}
          </p>
        )}
        
        {game.release_date && (
          <p className="text-sm text-gray-600 mb-1">
            <span className="font-medium">Released:</span> {new Date(game.release_date).getFullYear()}
          </p>
        )}
        
        {game.metacritic_score && (
          <div className="flex items-center mt-2">
            <span className="text-sm font-medium text-gray-700 mr-2">Score:</span>
            <span className={`px-2 py-1 rounded text-sm font-bold ${
              game.metacritic_score >= 80 ? 'bg-green-100 text-green-800' :
              game.metacritic_score >= 60 ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {game.metacritic_score}
            </span>
          </div>
        )}
        
        {game.description && (
          <p className="text-sm text-gray-600 mt-3 line-clamp-3">
            {game.description}
          </p>
        )}
      </div>
    </div>
  );
}
