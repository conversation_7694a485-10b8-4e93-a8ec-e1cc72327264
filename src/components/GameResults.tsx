import { useState } from 'react';
import { Game } from '../types';
import { GameModal } from './GameModal';

interface GameResultsProps {
  games: Game[];
  loading: boolean;
  error: string | null;
}

export function GameResults({ games, loading, error }: GameResultsProps) {
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleGameClick = (game: Game) => {
    setSelectedGame(game);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedGame(null);
  };
  if (loading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Searching for games...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Search Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (games.length === 0 && !loading && !error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🎮</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No games found</h3>
          <p className="text-gray-600">
            Try adjusting your search terms or platform filters
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          Game Results
        </h2>
        <p className="text-gray-600 mt-1">
          {games.length === 0
            ? "No games match your current filters"
            : `Showing ${games.length} platform versions from IGDB and RAWG databases`
          }
        </p>
        {games.length > 0 && (
          <div className="mt-2 flex gap-4 text-sm">
            <span className="flex items-center gap-1">
              <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
              IGDB: {games.filter(g => g.id.startsWith('igdb_')).length} games
            </span>
            <span className="flex items-center gap-1">
              <span className="w-3 h-3 bg-green-500 rounded-full"></span>
              RAWG: {games.filter(g => g.id.startsWith('rawg_')).length} games
            </span>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {games.map((game) => (
          <GameCard key={game.id} game={game} onClick={() => handleGameClick(game)} />
        ))}
      </div>

      <GameModal
        game={selectedGame}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}

interface GameCardProps {
  game: Game;
  onClick: () => void;
}

function GameCard({ game, onClick }: GameCardProps) {
  const getPlatformIcon = (platform: string) => {
    const platformLower = platform?.toLowerCase() || '';
    if (platformLower.includes('playstation') || platformLower.includes('ps')) return '🎮';
    if (platformLower.includes('xbox')) return '🎯';
    if (platformLower.includes('nintendo') || platformLower.includes('switch')) return '🕹️';
    if (platformLower.includes('pc') || platformLower.includes('windows')) return '💻';
    if (platformLower.includes('mac')) return '🖥️';
    if (platformLower.includes('ios') || platformLower.includes('iphone')) return '📱';
    if (platformLower.includes('android')) return '🤖';
    if (platformLower.includes('steam')) return '🚂';
    return '🎮';
  };

  return (
    <div
      className="group cursor-pointer transform transition-all duration-200 hover:scale-105 hover:shadow-xl"
      onClick={onClick}
    >
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Cover Image */}
        <div className="aspect-[3/4] bg-gray-200 relative">
          {game.cover_image ? (
            <img
              src={game.cover_image}
              alt={game.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <div className="text-center text-gray-400">
                <div className="text-4xl mb-2">🎮</div>
                <div className="text-sm">No Image</div>
              </div>
            </div>
          )}

          {/* Overlay with platform and source */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200">
            <div className="absolute top-2 left-2 flex gap-1">
              {/* Platform Badge */}
              {game.platform && (
                <span className="px-2 py-1 bg-black bg-opacity-70 text-white text-xs rounded-md font-medium backdrop-blur-sm">
                  {getPlatformIcon(game.platform)}
                </span>
              )}

              {/* Source Badge */}
              <span className={`px-2 py-1 text-xs rounded-md font-medium backdrop-blur-sm ${
                game.id.startsWith('igdb_')
                  ? 'bg-blue-500 bg-opacity-90 text-white'
                  : 'bg-green-500 bg-opacity-90 text-white'
              }`}>
                {game.id.startsWith('igdb_') ? 'IGDB' : 'RAWG'}
              </span>
            </div>

            {/* Rating Badge */}
            {game.metacritic_score && (
              <div className="absolute top-2 right-2">
                <span className={`px-2 py-1 rounded-md text-xs font-bold backdrop-blur-sm ${
                  game.metacritic_score >= 80 ? 'bg-green-500 bg-opacity-90 text-white' :
                  game.metacritic_score >= 60 ? 'bg-yellow-500 bg-opacity-90 text-white' :
                  'bg-red-500 bg-opacity-90 text-white'
                }`}>
                  {game.metacritic_score}
                </span>
              </div>
            )}

            {/* Title overlay on hover */}
            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-200">
              <h3 className="font-semibold text-sm line-clamp-2">{game.title}</h3>
              {game.platform && (
                <p className="text-xs text-gray-300 mt-1">{game.platform}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
