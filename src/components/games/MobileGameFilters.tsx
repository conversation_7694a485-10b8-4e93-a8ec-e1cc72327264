import React, { useState } from 'react';
import { Filter, X, ChevronDown, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Checkbox } from '@/components/ui/checkbox';
import { useGameStore } from '@/stores/useGameStore';
import { Platform, Genre } from '@/types';
import { TouchFriendlyCard } from '@/components/ui/touch-friendly-card';
import { motion, AnimatePresence } from 'framer-motion';

const platforms: Platform[] = ['PC', 'Xbox 360'];
const genres: Genre[] = [
  'Action', 'Adventure', 'RPG', 'Strategy', 'Sports',
  'Racing', 'Simulation', 'Puzzle', 'Fighting', 'Shooter',
  'Horror', 'Platformer', 'Indie'
];

export const MobileGameFilters: React.FC = () => {
  const { filters, updateFilters, sortBy, sortOrder, updateSort } = useGameStore();
  const [isOpen, setIsOpen] = useState(false);

  const clearAllFilters = () => {
    updateFilters({
      platform: undefined,
      genres: [],
      status: undefined,
      rating: undefined,
    });
  };

  const hasActiveFilters = filters.platform || filters.genres.length > 0 || filters.status || filters.rating;
  const activeFiltersCount = 
    (filters.platform ? 1 : 0) + 
    filters.genres.length + 
    (filters.status ? 1 : 0) + 
    (filters.rating ? 1 : 0);

  return (
    <>
      {/* Mobile Filter Summary Bar */}
      <div className="lg:hidden mb-4">
        <div className="flex items-center gap-2 overflow-x-auto pb-2">
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button 
                variant="outline" 
                size="sm"
                className="flex-shrink-0 h-9 px-3 touch-manipulation"
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filters
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[85vh] overflow-y-auto">
              <SheetHeader>
                <SheetTitle className="text-left">Filter & Sort Games</SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <MobileFilterContent
                  filters={filters}
                  updateFilters={updateFilters}
                  sortBy={sortBy}
                  sortOrder={sortOrder}
                  updateSort={updateSort}
                  clearAllFilters={clearAllFilters}
                  hasActiveFilters={hasActiveFilters}
                  onClose={() => setIsOpen(false)}
                />
              </div>
            </SheetContent>
          </Sheet>

          {/* Sort Quick Access */}
          <Select 
            value={`${sortBy}-${sortOrder}`} 
            onValueChange={(value) => {
              const [newSortBy, newSortOrder] = value.split('-');
              updateSort(newSortBy, newSortOrder as 'asc' | 'desc');
            }}
          >
            <SelectTrigger className="w-auto h-9 px-3 touch-manipulation">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title-asc">Title A-Z</SelectItem>
              <SelectItem value="title-desc">Title Z-A</SelectItem>
              <SelectItem value="date_added-desc">Recently Added</SelectItem>
              <SelectItem value="date_added-asc">Oldest First</SelectItem>
              <SelectItem value="rating-desc">Highest Rated</SelectItem>
              <SelectItem value="rating-asc">Lowest Rated</SelectItem>
            </SelectContent>
          </Select>

          {/* Active Filters */}
          <AnimatePresence>
            {hasActiveFilters && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex items-center gap-2"
              >
                <Button 
                  variant="outline"
                  size="sm"
                  onClick={clearAllFilters}
                  className="h-9 px-3 text-xs touch-manipulation"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear All
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Desktop Filters (Hidden on mobile) */}
      <div className="hidden lg:block">
        <TouchFriendlyCard className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters & Sort</span>
            </div>
            {hasActiveFilters && (
              <Button 
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="h-8 px-3 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* Platform Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Platform</label>
              <Select 
                value={filters.platform || ""} 
                onValueChange={(value) => updateFilters({ platform: value as Platform })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="All Platforms" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Platforms</SelectItem>
                  {platforms.map(platform => (
                    <SelectItem key={platform} value={platform}>{platform}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select 
                value={filters.status || ""} 
                onValueChange={(value) => updateFilters({ status: value })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  <SelectItem value="not_played">Not Played</SelectItem>
                  <SelectItem value="playing">Playing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="dropped">Dropped</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Rating Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Rating</label>
              <Select 
                value={filters.rating || ""} 
                onValueChange={(value) => updateFilters({ rating: value })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="All Ratings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Ratings</SelectItem>
                  <SelectItem value="5">5 Stars</SelectItem>
                  <SelectItem value="4">4+ Stars</SelectItem>
                  <SelectItem value="3">3+ Stars</SelectItem>
                  <SelectItem value="2">2+ Stars</SelectItem>
                  <SelectItem value="1">1+ Stars</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort by</label>
              <Select 
                value={`${sortBy}-${sortOrder}`} 
                onValueChange={(value) => {
                  const [newSortBy, newSortOrder] = value.split('-');
                  updateSort(newSortBy, newSortOrder as 'asc' | 'desc');
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="title-asc">Title A-Z</SelectItem>
                  <SelectItem value="title-desc">Title Z-A</SelectItem>
                  <SelectItem value="date_added-desc">Recently Added</SelectItem>
                  <SelectItem value="date_added-asc">Oldest First</SelectItem>
                  <SelectItem value="rating-desc">Highest Rated</SelectItem>
                  <SelectItem value="rating-asc">Lowest Rated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TouchFriendlyCard>
      </div>
    </>
  );
};

const MobileFilterContent: React.FC<{
  filters: any;
  updateFilters: any;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  updateSort: any;
  clearAllFilters: () => void;
  hasActiveFilters: boolean;
  onClose: () => void;
}> = ({ 
  filters, 
  updateFilters, 
  sortBy, 
  sortOrder, 
  updateSort, 
  clearAllFilters, 
  hasActiveFilters,
  onClose 
}) => {
  return (
    <div className="space-y-6">
      {/* Sort Section */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Sort</h3>
        <div className="grid grid-cols-1 gap-3">
          <Select 
            value={`${sortBy}-${sortOrder}`} 
            onValueChange={(value) => {
              const [newSortBy, newSortOrder] = value.split('-');
              updateSort(newSortBy, newSortOrder as 'asc' | 'desc');
            }}
          >
            <SelectTrigger className="w-full h-12 text-base">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title-asc">Title A-Z</SelectItem>
              <SelectItem value="title-desc">Title Z-A</SelectItem>
              <SelectItem value="date_added-desc">Recently Added</SelectItem>
              <SelectItem value="date_added-asc">Oldest First</SelectItem>
              <SelectItem value="rating-desc">Highest Rated</SelectItem>
              <SelectItem value="rating-asc">Lowest Rated</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Filter Section */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold">Filters</h3>
        
        {/* Platform Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Platform</label>
          <Select 
            value={filters.platform || ""} 
            onValueChange={(value) => updateFilters({ platform: value as Platform })}
          >
            <SelectTrigger className="w-full h-12 text-base">
              <SelectValue placeholder="All Platforms" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Platforms</SelectItem>
              {platforms.map(platform => (
                <SelectItem key={platform} value={platform}>{platform}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Status</label>
          <Select 
            value={filters.status || ""} 
            onValueChange={(value) => updateFilters({ status: value })}
          >
            <SelectTrigger className="w-full h-12 text-base">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Status</SelectItem>
              <SelectItem value="not_played">Not Played</SelectItem>
              <SelectItem value="playing">Playing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="dropped">Dropped</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Rating Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Rating</label>
          <Select 
            value={filters.rating || ""} 
            onValueChange={(value) => updateFilters({ rating: value })}
          >
            <SelectTrigger className="w-full h-12 text-base">
              <SelectValue placeholder="All Ratings" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Ratings</SelectItem>
              <SelectItem value="5">5 Stars</SelectItem>
              <SelectItem value="4">4+ Stars</SelectItem>
              <SelectItem value="3">3+ Stars</SelectItem>
              <SelectItem value="2">2+ Stars</SelectItem>
              <SelectItem value="1">1+ Stars</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Genres Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Genres</label>
          <div className="grid grid-cols-2 gap-2">
            {genres.map(genre => (
              <div key={genre} className="flex items-center space-x-2 p-2 rounded-lg border">
                <Checkbox
                  id={genre}
                  checked={filters.genres.includes(genre)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateFilters({ genres: [...filters.genres, genre] });
                    } else {
                      updateFilters({ genres: filters.genres.filter((g: string) => g !== genre) });
                    }
                  }}
                />
                <label htmlFor={genre} className="text-sm cursor-pointer">{genre}</label>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4 border-t">
        {hasActiveFilters && (
          <Button 
            variant="outline"
            onClick={clearAllFilters}
            className="flex-1 h-12 text-base"
          >
            Clear All
          </Button>
        )}
        <Button 
          onClick={onClose}
          className="flex-1 h-12 text-base"
        >
          Apply Filters
        </Button>
      </div>
    </div>
  );
};