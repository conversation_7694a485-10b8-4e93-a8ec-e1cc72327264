import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface TouchFriendlyCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'default' | 'interactive' | 'elevated';
  size?: 'sm' | 'md' | 'lg';
}

const variants = {
  default: 'bg-card border border-border/40 shadow-sm',
  interactive: 'bg-card border border-border/40 shadow-sm hover:shadow-md active:shadow-lg transition-shadow duration-200',
  elevated: 'bg-card border border-border/40 shadow-lg hover:shadow-xl active:shadow-2xl transition-shadow duration-200'
};

const sizes = {
  sm: 'p-3 sm:p-4',
  md: 'p-4 sm:p-6',
  lg: 'p-6 sm:p-8'
};

export const TouchFriendlyCard: React.FC<TouchFriendlyCardProps> = ({
  children,
  className,
  onClick,
  disabled = false,
  variant = 'default',
  size = 'md'
}) => {
  const isInteractive = !!onClick && !disabled;

  return (
    <motion.div
      className={cn(
        'rounded-xl backdrop-blur-sm',
        variants[variant],
        sizes[size],
        isInteractive && 'cursor-pointer',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={!disabled ? onClick : undefined}
      whileHover={isInteractive ? { y: -2 } : undefined}
      whileTap={isInteractive ? { scale: 0.98 } : undefined}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {children}
    </motion.div>
  );
};