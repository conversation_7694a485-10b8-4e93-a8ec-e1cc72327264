import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MobileGameGridProps {
  children: React.ReactNode;
  className?: string;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
}

const columnClasses = {
  1: 'grid-cols-1',
  2: 'grid-cols-2',
  3: 'grid-cols-2 sm:grid-cols-3',
  4: 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4'
};

const gapClasses = {
  sm: 'gap-2 sm:gap-3',
  md: 'gap-3 sm:gap-4',
  lg: 'gap-4 sm:gap-6'
};

export const MobileGameGrid: React.FC<MobileGameGridProps> = ({
  children,
  className,
  columns = 3,
  gap = 'md'
}) => {
  return (
    <motion.div
      className={cn(
        'grid w-full',
        columnClasses[columns],
        gapClasses[gap],
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
};