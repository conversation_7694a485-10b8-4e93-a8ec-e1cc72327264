import React, { useState } from 'react';
import { motion, useMotionValue, useTransform, PanInfo } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SwipeableCardProps {
  children: React.ReactNode;
  className?: string;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onTap?: () => void;
  swipeThreshold?: number;
  disabled?: boolean;
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  className,
  onSwipeLeft,
  onSwipeRight,
  onTap,
  swipeThreshold = 150,
  disabled = false
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const x = useMotionValue(0);
  const background = useTransform(
    x,
    [-swipeThreshold, 0, swipeThreshold],
    ['#ef4444', '#ffffff', '#10b981']
  );
  const opacity = useTransform(x, [-swipeThreshold, 0, swipeThreshold], [0.8, 1, 0.8]);
  const leftIndicatorOpacity = useTransform(x, [-swipeThreshold, 0], [1, 0]);
  const rightIndicatorOpacity = useTransform(x, [0, swipeThreshold], [0, 1]);

  const handlePan = (_: MouseEvent | TouchEvent, info: PanInfo) => {
    if (disabled) return;
    
    const offset = info.offset.x;
    const velocity = info.velocity.x;

    if (Math.abs(offset) > swipeThreshold || Math.abs(velocity) > 500) {
      if (offset > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (offset < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    }
  };

  return (
    <motion.div
      className={cn(
        'relative overflow-hidden rounded-xl bg-card border border-border/40 shadow-sm touch-manipulation',
        disabled && 'opacity-50',
        className
      )}
      style={{
        x,
        background: onSwipeLeft || onSwipeRight ? background : undefined,
        opacity
      }}
      drag={!disabled && (onSwipeLeft || onSwipeRight) ? 'x' : false}
      dragConstraints={{ left: 0, right: 0 }}
      dragElastic={0.2}
      onDragEnd={handlePan}
      onTapStart={() => setIsPressed(true)}
      onTapCancel={() => setIsPressed(false)}
      onTap={() => {
        setIsPressed(false);
        if (!disabled && onTap) onTap();
      }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <motion.div
        className="relative z-10"
        animate={isPressed ? { scale: 0.98 } : { scale: 1 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
      >
        {children}
      </motion.div>

      {/* Swipe indicators */}
      {(onSwipeLeft || onSwipeRight) && !disabled && (
        <>
          {onSwipeLeft && (
            <motion.div
              className="absolute left-4 top-1/2 -translate-y-1/2 text-red-500 opacity-0 pointer-events-none"
              style={{ opacity: leftIndicatorOpacity }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </motion.div>
          )}
          {onSwipeRight && (
            <motion.div
              className="absolute right-4 top-1/2 -translate-y-1/2 text-green-500 opacity-0 pointer-events-none"
              style={{ opacity: rightIndicatorOpacity }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </motion.div>
          )}
        </>
      )}
    </motion.div>
  );
};