import { useState } from 'react';

interface PlatformFilterProps {
  onFilterChange: (selectedPlatforms: string[]) => void;
  gameCount: number;
  allGames?: Array<{ platform?: string }>;
}

interface PlatformOption {
  id: string;
  name: string;
  icon: string;
  color: string;
  keywords: string[];
}

const platformOptions: PlatformOption[] = [
  {
    id: 'playstation',
    name: 'PlayStation',
    icon: '🎮',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    keywords: ['playstation', 'ps1', 'ps2', 'ps3', 'ps4', 'ps5', 'psp', 'vita']
  },
  {
    id: 'xbox',
    name: 'Xbox',
    icon: '🎯',
    color: 'bg-green-100 text-green-800 border-green-200',
    keywords: ['xbox', 'xbox 360', 'xbox one', 'xbox series']
  },
  {
    id: 'nintendo',
    name: 'Nintendo',
    icon: '🕹️',
    color: 'bg-red-100 text-red-800 border-red-200',
    keywords: ['nintendo', 'switch', 'wii', 'gamecube', '3ds', 'ds', 'nes', 'snes']
  },
  {
    id: 'pc',
    name: 'PC',
    icon: '💻',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    keywords: ['pc', 'windows', 'microsoft windows', 'steam', 'epic']
  },
  {
    id: 'mac',
    name: 'Mac',
    icon: '🖥️',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    keywords: ['mac', 'macos', 'apple']
  },
  {
    id: 'mobile',
    name: 'Mobile',
    icon: '📱',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    keywords: ['ios', 'android', 'iphone', 'ipad', 'mobile']
  },
  {
    id: 'steam',
    name: 'Steam Deck',
    icon: '🚂',
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    keywords: ['steam deck', 'steamdeck']
  }
];

export function PlatformFilter({ onFilterChange, gameCount, allGames = [] }: PlatformFilterProps) {
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [showAll, setShowAll] = useState(true);

  const handlePlatformToggle = (platformId: string) => {
    let newSelected: string[];
    
    if (selectedPlatforms.includes(platformId)) {
      newSelected = selectedPlatforms.filter(id => id !== platformId);
    } else {
      newSelected = [...selectedPlatforms, platformId];
    }
    
    setSelectedPlatforms(newSelected);
    setShowAll(newSelected.length === 0);
    onFilterChange(newSelected);
  };

  const handleShowAll = () => {
    setSelectedPlatforms([]);
    setShowAll(true);
    onFilterChange([]);
  };

  return (
    <div className="w-full bg-white border-b border-gray-200 sticky top-0 z-10 shadow-sm backdrop-blur-sm bg-white/95">
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Filter by Platform</h3>
          <div className="text-sm text-gray-600">
            {gameCount} games found
          </div>
        </div>
        
        <div className="flex flex-wrap gap-3 items-center">
          {/* Show All Button */}
          <button
            onClick={handleShowAll}
            className={`px-4 py-2 rounded-lg border-2 font-medium transition-all duration-200 ${
              showAll
                ? 'bg-gray-900 text-white border-gray-900'
                : 'bg-white text-gray-700 border-gray-300 hover:border-gray-400'
            }`}
          >
            🌐 All Platforms
          </button>

          {/* Platform Filter Buttons */}
          {platformOptions.map((platform) => {
            const isSelected = selectedPlatforms.includes(platform.id);
            return (
              <button
                key={platform.id}
                onClick={() => handlePlatformToggle(platform.id)}
                className={`px-4 py-2 rounded-lg border-2 font-medium transition-all duration-200 ${
                  isSelected
                    ? platform.color.replace('100', '200').replace('800', '900') + ' border-current'
                    : platform.color + ' hover:shadow-md border-current'
                }`}
                style={{
                  transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                  boxShadow: isSelected ? '0 4px 12px rgba(0,0,0,0.15)' : undefined
                }}
              >
                <span className="mr-2">{platform.icon}</span>
                {platform.name}
              </button>
            );
          })}
        </div>

        {/* Active Filters Display */}
        {selectedPlatforms.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Active filters:</span>
              {selectedPlatforms.map((platformId) => {
                const platform = platformOptions.find(p => p.id === platformId);
                return platform ? (
                  <span
                    key={platformId}
                    className={`px-2 py-1 rounded-md text-xs font-medium ${platform.color}`}
                  >
                    {platform.icon} {platform.name}
                  </span>
                ) : null;
              })}
              <button
                onClick={handleShowAll}
                className="ml-2 text-xs text-red-600 hover:text-red-800 underline"
              >
                Clear all
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper function to check if a game matches the selected platforms
export function gameMatchesPlatformFilter(gamePlatform: string, selectedPlatforms: string[]): boolean {
  if (selectedPlatforms.length === 0) return true;
  
  const platformLower = gamePlatform?.toLowerCase() || '';
  
  return selectedPlatforms.some(selectedId => {
    const platform = platformOptions.find(p => p.id === selectedId);
    return platform?.keywords.some(keyword => 
      platformLower.includes(keyword.toLowerCase())
    ) || false;
  });
}
